import pandas as pd
import numpy as np
from datetime import datetime

def convert_timestamp_to_string(df, timestamp_column, format='%Y-%m-%d'):
    """
    将DataFrame中的timestamp列转换为指定格式的字符串
    
    参数:
    df: pandas DataFrame
    timestamp_column: 包含timestamp的列名
    format: 日期格式字符串，默认为'%Y-%m-%d'
    
    返回:
    df_copy: 转换后的DataFrame副本
    """
    # 创建DataFrame的副本，避免修改原始数据
    df_copy = df.copy()
    
    # 确保timestamp列是datetime类型
    if not pd.api.types.is_datetime64_any_dtype(df_copy[timestamp_column]):
        try:
            # 尝试将列转换为datetime类型
            df_copy[timestamp_column] = pd.to_datetime(df_copy[timestamp_column])
        except Exception as e:
            print(f"无法将列 '{timestamp_column}' 转换为datetime类型: {e}")
            return df
    
    # 使用dt.strftime()将datetime转换为指定格式的字符串
    df_copy[timestamp_column] = df_copy[timestamp_column].dt.strftime(format)
    
    return df_copy

# 示例用法
if __name__ == "__main__":
    # 创建一个包含timestamp的示例DataFrame
    dates = [
        '2016-05-04 12:30:45',
        '2017-06-15 08:20:30',
        '2018-07-22 15:45:10',
        '2019-08-30 23:59:59',
        '2020-09-01 00:00:01'
    ]
    
    df = pd.DataFrame({
        'timestamp': pd.to_datetime(dates),
        'value': np.random.rand(5)
    })
    
    print("原始DataFrame:")
    print(df)
    print("\ntimestamp列的数据类型:", df['timestamp'].dtype)
    
    # 转换为'YYYY-MM-DD'格式的字符串
    df_ymd = convert_timestamp_to_string(df, 'timestamp')
    print("\n转换为YYYY-MM-DD格式:")
    print(df_ymd)
    print("\ntimestamp列的数据类型:", df_ymd['timestamp'].dtype)
    
    # 转换为其他格式
    df_custom = convert_timestamp_to_string(df, 'timestamp', format='%Y年%m月%d日')
    print("\n转换为自定义格式:")
    print(df_custom)
    
    # 直接使用pandas的方法（不使用自定义函数）
    print("\n直接使用pandas的dt.strftime()方法:")
    print(df['timestamp'].dt.strftime('%Y-%m-%d'))
    
    # 处理不同格式的日期字符串
    string_dates = ['2016/05/04', '2017-06-15', '20180722', '2019.08.30', '01-Sep-2020']
    df_str = pd.DataFrame({
        'date_string': string_dates,
        'value': np.random.rand(5)
    })
    
    print("\n包含字符串日期的DataFrame:")
    print(df_str)
    
    # 先转换为datetime，再转换为标准格式字符串
    df_str['date_string'] = pd.to_datetime(df_str['date_string'])
    df_str['date_string'] = df_str['date_string'].dt.strftime('%Y-%m-%d')
    
    print("\n转换后的DataFrame:")
    print(df_str)
