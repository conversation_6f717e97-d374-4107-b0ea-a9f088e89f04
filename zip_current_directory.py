import os
import zipfile
from datetime import datetime

def zip_current_directory(output_filename=None, exclude_files=None):
    """
    将当前目录下的所有文件打包为一个zip文件
    
    参数:
    output_filename: 输出的zip文件名，默认为'backup_当前日期时间.zip'
    exclude_files: 要排除的文件列表，默认排除生成的zip文件和脚本本身
    
    返回:
    zip_path: 生成的zip文件的完整路径
    """
    # 获取当前目录
    current_dir = os.getcwd()
    
    # 如果没有指定输出文件名，使用默认名称
    if output_filename is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"backup_{current_time}.zip"
    
    # 确保文件名以.zip结尾
    if not output_filename.endswith('.zip'):
        output_filename += '.zip'
    
    # 获取zip文件的完整路径
    zip_path = os.path.join(current_dir, output_filename)
    
    # 默认排除的文件
    if exclude_files is None:
        exclude_files = [output_filename, os.path.basename(__file__)]
    else:
        exclude_files.append(output_filename)
        exclude_files.append(os.path.basename(__file__))
    
    # 创建一个新的zip文件
    print(f"正在创建zip文件: {output_filename}")
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 遍历当前目录中的所有文件和子目录
            for root, dirs, files in os.walk(current_dir):
                for file in files:
                    # 检查文件是否在排除列表中
                    if file in exclude_files:
                        continue
                    
                    # 获取文件的完整路径
                    file_path = os.path.join(root, file)
                    
                    # 计算相对路径，以便在zip文件中保持目录结构
                    rel_path = os.path.relpath(file_path, current_dir)
                    
                    # 将文件添加到zip文件中
                    print(f"添加文件: {rel_path}")
                    zipf.write(file_path, rel_path)
        
        print(f"压缩完成! 文件保存为: {zip_path}")
        print(f"压缩文件大小: {os.path.getsize(zip_path) / (1024*1024):.2f} MB")
        
        return zip_path
    
    except Exception as e:
        print(f"压缩过程中出错: {e}")
        return None

if __name__ == "__main__":
    # 可以自定义输出文件名和排除的文件
    # zip_current_directory("my_backup.zip", ["some_file_to_exclude.txt"])
    
    # 使用默认设置
    zip_path = zip_current_directory()
    
    if zip_path:
        print(f"您可以在以下位置找到压缩文件: {zip_path}")
