import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class MomentumVolumeStrategy:
    def __init__(self):
        # 策略参数
        self.holding_days = 2        # 最大持仓天数（短周期）
        self.position_size = 0.15    # 单个股票仓位
        self.max_positions = 3       # 最大同时持仓数
        self.stop_loss = 0.025      # 止损比例（相对严格）
        self.profit_target = 0.04    # 止盈比例（快速获利）
        
    def calculate_indicators(self, stock_data):
        """计算技术指标"""
        df = stock_data.copy()
        
        # 计算成交量指标
        df['volume_change'] = df['volume'].pct_change()
        df['volume_ma3'] = df['volume'].rolling(3).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma3']
        
        # 计算价格动量
        df['price_change'] = df['close'].pct_change()
        df['momentum_1d'] = df['price_change']
        df['momentum_2d'] = df['close'].pct_change(2)
        
        # 计算资金流向（简化版）
        df['money_flow'] = df['volume'] * df['close']
        df['money_flow_change'] = df['money_flow'].pct_change()
        
        # 计算振幅
        df['amplitude'] = (df['high'] - df['low']) / df['close'].shift(1)
        
        # 计算强度指标
        df['strength'] = df['price_change'] * df['volume_ratio']
        
        return df

    def select_stocks(self, stock_data):
        """选股逻辑"""
        selected_stocks = []
        
        for stock in stock_data:
            df = self.calculate_indicators(stock)
            
            # 1. 资金流入信号
            money_flow_signal = (
                df['money_flow_change'].iloc[-1] > 0.8 and    # 资金大幅流入
                df['volume_ratio'].iloc[-1] > 2.5 and         # 显著放量
                df['price_change'].iloc[-1] > 0               # 价格上涨
            )
            
            # 2. 强度突破信号
            strength_signal = (
                df['strength'].iloc[-1] > 0.05 and            # 强度指标突破
                df['amplitude'].iloc[-1] < 0.06               # 振幅适中
            )
            
            # 3. 连续性信号
            momentum_signal = (
                df['momentum_1d'].iloc[-1] > 0.015 and        # 当日涨幅适中
                df['momentum_2d'].iloc[-1] > 0.02             # 短期趋势向上
            )
            
            # 4. 排除过度追高
            not_overchased = (
                df['price_change'].iloc[-1] < 0.07 and        # 避免涨幅过大
                df['amplitude'].rolling(3).mean().iloc[-1] < 0.08  # 近期振幅正常
            )
            
            if (money_flow_signal or strength_signal) and momentum_signal and not_overchased:
                selected_stocks.append(stock)
        
        return selected_stocks

    def position_management(self, positions):
        """仓位管理"""
        for pos in positions:
            # 快速止损
            if pos.current_return < -self.stop_loss:
                self.sell_stock(pos.stock_code)
                continue
            
            # 分段止盈
            if pos.current_return > self.profit_target:
                if pos.holding_days == 1:
                    self.sell_portion(pos.stock_code, 0.6)    # 首日盈利即卖出大部分
                else:
                    self.sell_stock(pos.stock_code)           # 第二日全部卖出
            
            # 持仓时间管理
            if pos.holding_days >= self.holding_days:
                self.sell_stock(pos.stock_code)

    def risk_control(self, market_data):
        """风险控制"""
        df = self.calculate_indicators(market_data)
        
        # 1. 市场活跃度检查
        market_active = (
            df['volume_ratio'].iloc[-1] > 0.8 and          # 市场成交活跃
            df['money_flow_change'].iloc[-1] > -0.2        # 资金未大幅流出
        )
        
        # 2. 市场波动检查
        market_stable = (
            df['amplitude'].iloc[-1] < 0.03 and            # 市场波动不剧烈
            abs(df['price_change'].iloc[-1]) < 0.015       # 市场涨跌幅适中
        )
        
        return market_active and market_stable

    def execute_trade(self, context):
        """策略执行"""
        # 1. 检查市场环境
        if not self.risk_control(context.market_data):
            return
        
        # 2. 持仓管理
        self.position_management(context.positions)
        
        # 3. 选股并建仓
        if len(context.positions) < self.max_positions:
            selected = self.select_stocks(context.stock_universe)
            # 按强度排序
            selected.sort(key=lambda x: x.strength, reverse=True)
            for stock in selected[:self.max_positions - len(context.positions)]:
                self.buy_stock(stock.code, self.position_size)