import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import pandas as pd

def train_random_forest(X, y, test_size=0.2, random_state=42, n_estimators=100, max_depth=None):
    """
    使用随机森林算法训练预测模型，并评估模型性能
    
    参数:
    X: 特征数据，一维或二维数组
    y: 目标标签，一维数组
    test_size: 测试集比例，默认0.2
    random_state: 随机种子，默认42
    n_estimators: 随机森林中树的数量，默认100
    max_depth: 树的最大深度，默认None表示不限制深度
    
    返回:
    model: 训练好的随机森林模型
    X_test: 测试集特征
    y_test: 测试集标签
    y_pred: 模型在测试集上的预测结果
    metrics: 包含评估指标的字典
    """
    # 确保X是二维数组（如果是一维的，转换为二维）
    if len(X.shape) == 1:
        X = X.reshape(-1, 1)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
    
    # 创建随机森林模型
    model = RandomForestRegressor(n_estimators=n_estimators, 
                                 max_depth=max_depth,
                                 random_state=random_state)
    
    # 训练模型
    model.fit(X_train, y_train)
    
    # 在测试集上进行预测
    y_pred = model.predict(X_test)
    
    # 计算评估指标
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # 将评估指标存储在字典中
    metrics = {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }
    
    return model, X_test, y_test, y_pred, metrics

def evaluate_and_visualize(X_test, y_test, y_pred, metrics):
    """
    评估模型性能并可视化结果
    
    参数:
    X_test: 测试集特征
    y_test: 测试集真实标签
    y_pred: 模型预测值
    metrics: 包含评估指标的字典
    """
    # 打印评估指标
    print("模型评估指标:")
    print(f"均方误差 (MSE): {metrics['MSE']:.4f}")
    print(f"均方根误差 (RMSE): {metrics['RMSE']:.4f}")
    print(f"平均绝对误差 (MAE): {metrics['MAE']:.4f}")
    print(f"决定系数 (R²): {metrics['R2']:.4f}")
    
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 创建图形
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 绘制真实值与预测值的散点图
    axes[0].scatter(y_test, y_pred, alpha=0.6)
    axes[0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
    axes[0].set_xlabel('真实值')
    axes[0].set_ylabel('预测值')
    axes[0].set_title('真实值 vs 预测值')
    
    # 绘制残差图
    residuals = y_test - y_pred
    axes[1].scatter(y_pred, residuals, alpha=0.6)
    axes[1].axhline(y=0, color='r', linestyle='--')
    axes[1].set_xlabel('预测值')
    axes[1].set_ylabel('残差')
    axes[1].set_title('残差图')
    
    plt.tight_layout()
    plt.show()
    
    return fig

# 示例用法
if __name__ == "__main__":
    # 生成示例数据
    np.random.seed(42)
    X = np.random.rand(100, 1) * 10  # 一维特征
    y = 2 * X.ravel() + 1 + np.random.randn(100) * 2  # 目标变量
    
    # 训练模型并评估
    model, X_test, y_test, y_pred, metrics = train_random_forest(X, y)
    
    # 评估并可视化结果
    evaluate_and_visualize(X_test, y_test, y_pred, metrics)
    
    # 使用模型进行新数据预测示例
    new_data = np.array([5.0, 7.0, 3.0]).reshape(-1, 1)
    predictions = model.predict(new_data)
    
    print("\n新数据预测结果:")
    for i, pred in enumerate(predictions):
        print(f"输入: {new_data[i][0]:.1f}, 预测: {pred:.4f}")
    
    # 特征重要性（如果有多个特征）
    if X.shape[1] > 1:
        feature_importance = pd.DataFrame({
            'Feature': [f'特征 {i}' for i in range(X.shape[1])],
            'Importance': model.feature_importances_
        }).sort_values('Importance', ascending=False)
        
        print("\n特征重要性:")
        print(feature_importance)
