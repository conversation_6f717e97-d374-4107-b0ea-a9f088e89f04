import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
from matplotlib.font_manager import FontProperties

def plot_max_drawdown(time_series, values, second_values=None, lambda_value=None):
    """
    绘制最大回撤曲线图，可选添加第二条曲线（使用不同的y轴刻度）

    参数:
    time_series: 时间序列集合，用于x轴
    values: 数值集合，用于主y轴
    second_values: 第二条曲线的数值集合，用于次y轴，可选参数
    lambda_value: lambda值，可选参数
    """
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

    # 创建图形和坐标轴
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # 绘制第一条曲线（蓝色）
    ax1.plot(time_series, values, label='上证指数', color='blue', linewidth=2)

    # 设置标题和轴标签
    ax1.set_title('上证指数2016--2022年最大回撤', fontsize=15)
    ax1.set_xlabel('日期', fontsize=12)
    ax1.set_ylabel('最大回撤', fontsize=12, color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')

    # 如果提供了第二条曲线的数据，则绘制第二条曲线（红色）
    if second_values is not None:
        # 创建第二个y轴
        ax2 = ax1.twinx()
        ax2.plot(time_series, second_values, label='第二曲线', color='red', linewidth=2)
        ax2.set_ylabel('第二曲线值', fontsize=12, color='red')
        ax2.tick_params(axis='y', labelcolor='red')

    # 添加lambda标签（如果提供）
    if lambda_value is not None:
        plt.text(0.02, 0.95, f'λ = {lambda_value}', transform=plt.gca().transAxes,
                 fontsize=12, verticalalignment='top')

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    if second_values is not None:
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='best')
    else:
        ax1.legend(loc='best')

    # 优化x轴日期显示
    ax1.tick_params(axis='x', rotation=45)
    fig.tight_layout()

    # 显示网格
    ax1.grid(True, linestyle='--', alpha=0.7)

    # 显示图形
    plt.show()

# 示例用法
if __name__ == "__main__":
    # 示例数据
    dates = np.array(['2016-01-01', '2017-01-01', '2018-01-01',
                      '2019-01-01', '2020-01-01', '2021-01-01', '2022-01-01'])
    drawdowns = np.array([-0.1, -0.15, -0.25, -0.18, -0.3, -0.12, -0.2])

    # 第二条曲线的示例数据（使用不同的数值范围）
    second_curve = np.array([3000, 3200, 2800, 3100, 3500, 3800, 3600])

    # 调用函数绘制图形（包含两条曲线）
    plot_max_drawdown(dates, drawdowns, second_values=second_curve, lambda_value=0.95)

    # 也可以只绘制一条曲线
    # plot_max_drawdown(dates, drawdowns, lambda_value=0.95)
