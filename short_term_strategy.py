import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class ShortTermStrategy:
    def __init__(self):
        # 策略参数
        self.holding_days = 3        # 最大持仓天数
        self.position_size = 0.15    # 单个股票仓位
        self.max_positions = 3       # 最大同时持仓数
        self.stop_loss = 0.03       # 止损比例
        self.profit_target = 0.05    # 止盈比例
        
    def calculate_indicators(self, stock_data):
        """计算技术指标"""
        df = stock_data.copy()
        
        # 计算短期均线
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        
        # 计算量能指标
        df['volume_ma5'] = df['volume'].rolling(5).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma5']
        
        # 计算涨跌幅
        df['price_change'] = df['close'].pct_change()
        
        # 计算波动率
        df['volatility'] = df['price_change'].rolling(5).std()
        
        return df

    def select_stocks(self, stock_data):
        """选股逻辑"""
        selected_stocks = []
        
        for stock in stock_data:
            df = self.calculate_indicators(stock)
            
            # 1. 量价配合信号
            volume_price_match = (
                df['volume_ratio'].iloc[-1] > 2.0 and      # 放量
                df['price_change'].iloc[-1] > 0.02 and     # 上涨
                df['close'].iloc[-1] > df['ma5'].iloc[-1]  # 站上5日线
            )
            
            # 2. 趋势反转信号
            trend_reversal = (
                df['ma5'].iloc[-1] > df['ma5'].iloc[-2] and  # 5日线上扬
                df['ma5'].iloc[-2] < df['ma5'].iloc[-3] and  # 之前下跌
                df['volume_ratio'].iloc[-1] > 1.5            # 有量支撑
            )
            
            # 3. 波动率检查
            low_volatility = df['volatility'].iloc[-1] < 0.02  # 波动率适中
            
            if (volume_price_match or trend_reversal) and low_volatility:
                selected_stocks.append(stock)
        
        return selected_stocks

    def position_management(self, positions):
        """仓位管理"""
        for pos in positions:
            # 及时止损
            if pos.current_return < -self.stop_loss:
                self.sell_stock(pos.stock_code)
                continue
            
            # 分批止盈
            if pos.current_return > self.profit_target:
                if pos.holding_days == 1:
                    self.sell_portion(pos.stock_code, 0.5)  # 首日盈利即卖出一半
                else:
                    self.sell_stock(pos.stock_code)  # 后续全部卖出
            
            # 持仓时间管理
            if pos.holding_days >= self.holding_days:
                self.sell_stock(pos.stock_code)

    def risk_control(self, market_data):
        """风险控制"""
        df = self.calculate_indicators(market_data)
        
        # 1. 大盘走势检查
        market_trend = (
            df['ma5'].iloc[-1] > df['ma10'].iloc[-1] and  # 短期均线多头
            df['volume_ratio'].iloc[-1] > 0.8             # 成交量正常
        )
        
        # 2. 市场情绪检查
        market_sentiment = df['volatility'].iloc[-1] < 0.015  # 市场波动不剧烈
        
        return market_trend and market_sentiment

    def execute_trade(self, context):
        """策略执行"""
        # 1. 检查市场环境
        if not self.risk_control(context.market_data):
            return
        
        # 2. 持仓管理
        self.position_management(context.positions)
        
        # 3. 选股并建仓
        if len(context.positions) < self.max_positions:
            selected = self.select_stocks(context.stock_universe)
            for stock in selected[:self.max_positions - len(context.positions)]:
                self.buy_stock(stock.code, self.position_size)
