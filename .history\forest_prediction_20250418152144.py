import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
import joblib

def predict_with_model(model, new_data, feature_names=None):
    """
    使用训练好的随机森林模型对新数据进行预测
    
    参数:
    model: 已训练好的随机森林模型
    new_data: 需要预测的新数据，可以是numpy数组、列表或DataFrame
    feature_names: 特征名称列表，如果new_data是DataFrame则不需要提供
    
    返回:
    predictions: 预测结果数组
    """
    # 检查输入数据类型并进行必要的转换
    if isinstance(new_data, pd.DataFrame):
        # 如果是DataFrame，直接使用
        X_new = new_data
    elif isinstance(new_data, list) or isinstance(new_data, np.ndarray):
        # 如果是列表或numpy数组
        if len(np.array(new_data).shape) == 1:
            # 如果是一维数组，转换为二维
            X_new = np.array(new_data).reshape(1, -1)
        else:
            X_new = np.array(new_data)
            
        # 如果提供了特征名称，转换为DataFrame
        if feature_names is not None:
            if len(feature_names) != X_new.shape[1]:
                print(f"警告: 特征名称数量({len(feature_names)})与数据特征数量({X_new.shape[1]})不匹配")
            else:
                X_new = pd.DataFrame(X_new, columns=feature_names)
    else:
        raise TypeError("输入数据类型不支持，请使用numpy数组、列表或DataFrame")
    
    # 使用模型进行预测
    predictions = model.predict(X_new)
    
    return predictions

def visualize_predictions(original_data, predictions, feature_index=0):
    """
    可视化预测结果
    
    参数:
    original_data: 原始输入数据
    predictions: 预测结果
    feature_index: 用于可视化的特征索引（如果是多特征数据）
    """
    plt.figure(figsize=(10, 6))
    
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 获取用于绘图的x值
    if isinstance(original_data, pd.DataFrame):
        x_values = original_data.iloc[:, feature_index].values
    else:
        x_values = np.array(original_data)
        if len(x_values.shape) > 1:
            x_values = x_values[:, feature_index]
    
    # 绘制散点图
    plt.scatter(x_values, predictions, c='blue', alpha=0.6, label='预测值')
    
    # 添加趋势线
    z = np.polyfit(x_values, predictions, 1)
    p = np.poly1d(z)
    plt.plot(x_values, p(x_values), 'r--', label=f'趋势线: y={z[0]:.2f}x+{z[1]:.2f}')
    
    plt.title('随机森林模型预测结果')
    plt.xlabel(f'特征 {feature_index}')
    plt.ylabel('预测值')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.show()

def save_predictions_to_file(predictions, output_file='predictions.csv', ids=None):
    """
    将预测结果保存到文件
    
    参数:
    predictions: 预测结果数组
    output_file: 输出文件名
    ids: 可选的ID列表，用于标识每个预测结果
    """
    # 创建DataFrame来保存结果
    if ids is not None:
        if len(ids) != len(predictions):
            print(f"警告: ID数量({len(ids)})与预测结果数量({len(predictions)})不匹配")
            # 使用较短的长度
            length = min(len(ids), len(predictions))
            result_df = pd.DataFrame({'ID': ids[:length], 'Prediction': predictions[:length]})
        else:
            result_df = pd.DataFrame({'ID': ids, 'Prediction': predictions})
    else:
        result_df = pd.DataFrame({'Prediction': predictions})
    
    # 保存到CSV文件
    result_df.to_csv(output_file, index=False)
    print(f"预测结果已保存到: {output_file}")
    
    return result_df

# 示例用法
if __name__ == "__main__":
    # 示例1: 加载已保存的模型（如果您之前保存了模型）
    # model = joblib.load('random_forest_model.joblib')
    
    # 示例2: 或者使用一个新训练的模型进行演示
    # 创建一个简单的随机森林模型
    X_train = np.random.rand(100, 3)  # 3个特征的训练数据
    y_train = 2*X_train[:, 0] + 3*X_train[:, 1] - X_train[:, 2] + np.random.randn(100)*0.5  # 模拟目标变量
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # 示例3: 保存模型供以后使用
    # joblib.dump(model, 'random_forest_model.joblib')
    
    # 准备新数据进行预测
    # 方式1: 使用numpy数组
    new_data_array = np.array([
        [0.5, 0.6, 0.7],  # 第一个样本
        [0.3, 0.2, 0.9],  # 第二个样本
        [0.8, 0.1, 0.2]   # 第三个样本
    ])
    
    # 方式2: 使用DataFrame
    feature_names = ['特征1', '特征2', '特征3']
    new_data_df = pd.DataFrame(new_data_array, columns=feature_names)
    
    # 使用模型进行预测
    predictions_array = predict_with_model(model, new_data_array)
    print("\n使用numpy数组的预测结果:")
    for i, pred in enumerate(predictions_array):
        print(f"样本 {i+1}: {pred:.4f}")
    
    predictions_df = predict_with_model(model, new_data_df)
    print("\n使用DataFrame的预测结果:")
    for i, pred in enumerate(predictions_df):
        print(f"样本 {i+1}: {pred:.4f}")
    
    # 可视化预测结果（使用第一个特征）
    visualize_predictions(new_data_array, predictions_array, feature_index=0)
    
    # 保存预测结果到CSV文件
    ids = [f"样本_{i+1}" for i in range(len(predictions_array))]
    result_df = save_predictions_to_file(predictions_array, 'forest_predictions.csv', ids)
    
    # 显示保存的结果
    print("\n保存的预测结果:")
    print(result_df)
