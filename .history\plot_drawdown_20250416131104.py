import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
from matplotlib.font_manager import FontProperties

def plot_max_drawdown(time_series, values, lambda_value=None):
    """
    绘制最大回撤曲线图
    
    参数:
    time_series: 时间序列集合，用于x轴
    values: 数值集合，用于y轴
    lambda_value: lambda值，可选参数
    """
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 创建图形和坐标轴
    plt.figure(figsize=(12, 6))
    
    # 绘制曲线
    plt.plot(time_series, values, label='上证指数', color='blue', linewidth=2)
    
    # 设置标题和轴标签
    plt.title('上证指数2016--2022年最大回撤', fontsize=15)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('最大回撤', fontsize=12)
    
    # 添加lambda标签（如果提供）
    if lambda_value is not None:
        plt.text(0.02, 0.95, f'λ = {lambda_value}', transform=plt.gca().transAxes, 
                 fontsize=12, verticalalignment='top')
    
    # 添加图例
    plt.legend(loc='best')
    
    # 优化x轴日期显示
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 显示网格
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 显示图形
    plt.show()

# 示例用法
if __name__ == "__main__":
    # 示例数据
    dates = np.array(['2016-01-01', '2017-01-01', '2018-01-01', 
                      '2019-01-01', '2020-01-01', '2021-01-01', '2022-01-01'])
    drawdowns = np.array([-0.1, -0.15, -0.25, -0.18, -0.3, -0.12, -0.2])
    
    # 调用函数绘制图形
    plot_max_drawdown(dates, drawdowns, lambda_value=0.95)
