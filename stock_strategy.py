import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class ChinaStockStrategy:
    def __init__(self):
        self.holding_period = 20  # 持仓周期（交易日）
        self.position_size = 0.2  # 单个股票仓位
        self.stop_loss = 0.08     # 止损线
        self.profit_target = 0.15 # 止盈目标
        
    def select_stocks(self, stock_data):
        """选股策略"""
        selected_stocks = []
        
        for stock in stock_data:
            # 1. 基本面筛选
            if (stock.pe_ratio < 15 and           # PE低估值
                stock.pb_ratio < 1.5 and          # PB较低
                stock.roe > 0.12 and              # ROE良好
                stock.debt_ratio < 0.6):          # 负债率可控
                
                # 2. 技术面筛选
                if (stock.price < stock.ma50 and  # 价格低于50日均线
                    stock.volume > stock.volume_ma20 * 1.5):  # 放量
                    
                    selected_stocks.append(stock)
        
        return selected_stocks
    
    def position_management(self, portfolio):
        """仓位管理"""
        for position in portfolio:
            # 止损检查
            if position.current_return < -self.stop_loss:
                self.sell_stock(position.stock_code)
                
            # 止盈检查
            elif position.current_return > self.profit_target:
                self.sell_half_position(position.stock_code)
            
            # 持仓时间检查
            if position.holding_days > self.holding_period:
                self.review_position(position)
    
    def risk_control(self):
        """风险控制"""
        # 1. 分散投资
        max_single_industry = 0.3  # 单一行业最大仓位
        
        # 2. 市场风险评估
        def check_market_risk(self):
            if (self.market_index.ma5 < self.market_index.ma10 and 
                self.market_index.volume < self.market_index.volume_ma5):
                return "high_risk"
            return "normal"