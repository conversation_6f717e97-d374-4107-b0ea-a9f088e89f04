import os
import zipfile
from datetime import datetime

def zip_specific_files(root_path, files_list, output_filename=None):
    """
    将指定目录下的特定文件打包为一个zip文件
    
    参数:
    root_path: 文件根目录路径
    files_list: 要压缩的文件名列表
    output_filename: 输出的zip文件名，默认为'backup_当前日期时间.zip'
    
    返回:
    zip_path: 生成的zip文件的完整路径
    """
    # 确保根目录路径存在
    if not os.path.exists(root_path) or not os.path.isdir(root_path):
        print(f"错误: 指定的根目录路径 '{root_path}' 不存在或不是一个目录")
        return None
    
    # 如果没有指定输出文件名，使用默认名称
    if output_filename is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"backup_{current_time}.zip"
    
    # 确保文件名以.zip结尾
    if not output_filename.endswith('.zip'):
        output_filename += '.zip'
    
    # 获取zip文件的完整路径（保存在当前工作目录）
    zip_path = os.path.join(os.getcwd(), output_filename)
    
    # 创建一个新的zip文件
    print(f"正在创建zip文件: {output_filename}")
    
    # 计数器
    files_added = 0
    files_not_found = 0
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 遍历要压缩的文件列表
            for file_name in files_list:
                # 获取文件的完整路径
                file_path = os.path.join(root_path, file_name)
                
                # 检查文件是否存在
                if not os.path.exists(file_path) or not os.path.isfile(file_path):
                    print(f"警告: 文件 '{file_name}' 不存在或不是一个文件，已跳过")
                    files_not_found += 1
                    continue
                
                # 将文件添加到zip文件中
                print(f"添加文件: {file_name}")
                zipf.write(file_path, file_name)  # 只保存文件名，不保留路径结构
                files_added += 1
        
        if files_added > 0:
            print(f"\n压缩完成! 文件保存为: {zip_path}")
            print(f"压缩文件大小: {os.path.getsize(zip_path) / (1024*1024):.2f} MB")
            print(f"成功添加: {files_added} 个文件")
            if files_not_found > 0:
                print(f"未找到: {files_not_found} 个文件")
            
            return zip_path
        else:
            print("没有找到任何要压缩的文件，未创建zip文件")
            # 删除空的zip文件
            if os.path.exists(zip_path):
                os.remove(zip_path)
            return None
    
    except Exception as e:
        print(f"压缩过程中出错: {e}")
        return None

# 示例用法
if __name__ == "__main__":
    # 示例：指定根目录和文件列表
    root_directory = "D:\\示例目录"  # 替换为您的根目录路径
    files_to_zip = [
        "file1.txt",
        "file2.docx",
        "image.jpg",
        "subfolder/file3.pdf"  # 如果需要包含子文件夹中的文件，请在files_list中指定相对路径
    ]
    
    # 自定义输出文件名
    output_zip = "my_files.zip"
    
    # 调用函数压缩文件
    zip_path = zip_specific_files(root_directory, files_to_zip, output_zip)
    
    if zip_path:
        print(f"您可以在以下位置找到压缩文件: {zip_path}")
